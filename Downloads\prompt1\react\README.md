# 🚀 Animated Portfolio Website

A stunning, modern portfolio website built with React, Framer Motion, and Tailwind CSS. Features beautiful animations, responsive design, and a professional layout to showcase your projects and skills.

## ✨ Features

- **Smooth Animations**: Powered by Framer Motion for fluid, professional animations
- **Responsive Design**: Looks great on all devices from mobile to desktop
- **Modern UI**: Clean, professional design with glassmorphism effects
- **Interactive Elements**: Hover effects, custom cursor, and scroll animations
- **Performance Optimized**: Lazy loading, optimized animations, and mobile-friendly
- **Accessibility**: WCAG compliant with proper focus management and reduced motion support
- **Customizable**: Easy to modify colors, content, and layout

## 🛠️ Technologies Used

- **React 19** - Modern React with hooks
- **Framer Motion** - Advanced animations and transitions
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful, customizable icons
- **React Router** - Client-side routing
- **Vite** - Fast build tool and development server

## 🚀 Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start the development server**
   ```bash
   npm run dev
   ```

3. **Open your browser**
   Navigate to `http://localhost:5173`

## 🎨 Customization Guide

### Personal Information

1. **Update Hero Section** (`src/components/Hero.jsx`):
   - Change "Your Name" to your actual name
   - Update the roles array with your skills
   - Modify the description text
   - Update social media links

2. **Update About Section** (`src/components/About.jsx`):
   - Modify the about text
   - Update skills and their levels
   - Change the quick facts section
   - Add your experience and location

3. **Update Projects** (`src/components/Projects.jsx`):
   - Replace the sample projects with your actual projects
   - Update project images, descriptions, and technologies
   - Add your GitHub and live demo links

4. **Update Contact Information** (`src/components/Contact.jsx`):
   - Change email, phone, and location
   - Update social media links
   - Modify the contact form (add backend integration if needed)

### Styling and Colors

1. **Color Scheme** (`tailwind.config.js`):
   - Modify the color palette in the theme.extend.colors section
   - Update gradient colors in `src/index.css`

2. **Fonts** (`src/index.css`):
   - Change the Google Fonts import
   - Update font families in Tailwind config

3. **Animations**:
   - Modify animation durations in component files
   - Add new animations in `tailwind.config.js`

## 📱 Mobile Optimization

The portfolio is fully responsive and includes:
- Touch-friendly navigation
- Optimized animations for mobile
- Reduced particle effects on mobile for better performance
- Proper viewport settings

## ♿ Accessibility Features

- Semantic HTML structure
- Proper ARIA labels
- Keyboard navigation support
- Reduced motion support for users who prefer it
- High contrast mode support
- Focus management

## 🔧 Build and Deployment

1. **Build for production**
   ```bash
   npm run build
   ```

2. **Preview the build**
   ```bash
   npm run preview
   ```

3. **Deploy to your preferred platform**:
   - **Vercel**: Connect your GitHub repo to Vercel
   - **Netlify**: Drag and drop the `dist` folder
   - **GitHub Pages**: Use GitHub Actions for automatic deployment

## 📦 Project Structure

```
src/
├── components/
│   ├── About.jsx          # About section with skills
│   ├── Contact.jsx        # Contact form and info
│   ├── CustomCursor.jsx   # Custom cursor effect
│   ├── Hero.jsx           # Hero section with typing animation
│   ├── LoadingScreen.jsx  # Initial loading animation
│   ├── Navbar.jsx         # Navigation bar
│   ├── ParticleBackground.jsx # Animated background
│   ├── Projects.jsx       # Projects showcase
│   └── ScrollToTop.jsx    # Scroll to top button
├── App.jsx               # Main app component
├── App.css              # Additional styles
├── index.css            # Global styles and Tailwind
└── main.jsx             # App entry point
```

## 🎯 Performance Tips

- Images are optimized and lazy-loaded
- Animations are GPU-accelerated
- Particles are disabled on mobile
- Code splitting is implemented
- Bundle size is optimized

---

**Happy coding!** 🎉 Customize this template to make it your own!
