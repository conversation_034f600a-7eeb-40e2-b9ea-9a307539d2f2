{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.1.0", "autoprefixer": "^10.4.21", "express": "^5.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.7.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}